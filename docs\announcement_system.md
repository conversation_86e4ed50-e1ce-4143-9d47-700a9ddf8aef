# 公告系统实时推送机制

## 概述

本文档描述了岱宗文脉应用中公告系统的实时推送机制改进。新的系统确保用户能够在公告更新后立即收到通知，无需等待长时间的缓存刷新。

## 主要改进

### 1. 缩短缓存检查间隔

- **原来**：24小时检查一次
- **现在**：
  - 应用在前台：每1分钟检查一次
  - 应用在后台：每5分钟检查一次

### 2. 应用生命周期感知

- 监听应用前台/后台状态变化
- 应用从后台切换到前台时自动检查公告更新
- 根据应用状态动态调整检查频率

### 3. WebSocket实时推送

- 建立WebSocket连接用于实时通信
- 服务器端公告更新时立即推送通知
- 自动重连机制确保连接稳定性
- 心跳机制保持连接活跃

### 4. 智能缓存策略

- 强制刷新功能，忽略时间限制
- 缓存失效机制优化
- 支持手动刷新和自动刷新

## 技术实现

### 前端（Flutter）

#### AnnouncementService 改进

```dart
class AnnouncementService extends GetxService {
  // 检查间隔（分钟）
  static const int _checkIntervalMinutes = 5;
  static const int _foregroundCheckIntervalMinutes = 1;
  
  // 应用生命周期状态
  bool _isAppInForeground = true;
  
  // 应用生命周期监听
  void _setupAppLifecycleListener() {
    WidgetsBinding.instance.addObserver(_AppLifecycleObserver(this));
  }
  
  // 强制刷新功能
  Future<void> forceRefreshAnnouncement() async {
    // 清除检查时间和已读状态
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_lastCheckTimeKey);
    await prefs.remove(_lastAnnouncementIdKey);
    
    await _fetchLatestAnnouncementAsync();
  }
}
```

#### WebSocketService 实现

```dart
class WebSocketService extends GetxService {
  WebSocketChannel? _channel;
  final RxBool isConnected = false.obs;
  
  // 处理公告更新通知
  void _handleAnnouncementUpdate(Map<String, dynamic> data) {
    final announcementService = Get.find<AnnouncementService>();
    announcementService.forceRefreshAnnouncement();
  }
}
```

### 后端（FastAPI）

#### WebSocket端点

```python
@app.websocket("/ws/announcements")
async def websocket_announcements(websocket: WebSocket):
    await websocket_endpoint(websocket)
```

#### 公告更新通知

```python
@router.post("/", response_model=AnnouncementResponse)
async def create_announcement(announcement: AnnouncementCreate, ...):
    # 创建公告
    db_announcement = Announcement(...)
    db.add(db_announcement)
    db.commit()
    
    # 发送WebSocket通知
    if db_announcement.is_active:
        await notify_announcement_update({
            "id": db_announcement.id,
            "title": db_announcement.title,
            "action": "created"
        })
```

## 使用方式

### 1. 自动推送

- 应用启动后自动建立WebSocket连接
- 服务器端公告更新时自动推送
- 用户无需任何操作即可收到最新公告

### 2. 手动刷新

```dart
// 普通刷新（遵循时间间隔）
await announcementService.refreshAnnouncement();

// 强制刷新（忽略时间间隔）
await announcementService.forceRefreshAnnouncement();
```

### 3. 缓存管理

```dart
// 清除所有缓存
await announcementService.clearCache();
```

## 配置选项

### 检查间隔配置

```dart
// 可在AnnouncementService中调整
static const int _checkIntervalMinutes = 5;           // 后台检查间隔
static const int _foregroundCheckIntervalMinutes = 1; // 前台检查间隔
```

### WebSocket配置

```dart
// 重连配置
static const int _maxReconnectAttempts = 5;
static const Duration _reconnectDelay = Duration(seconds: 5);
static const Duration _heartbeatInterval = Duration(seconds: 30);
```

## 监控和调试

### 连接状态监控

- WebSocket连接状态实时显示
- 连接失败时自动重连
- 连接统计信息

### 日志输出

```
🔔 初始化公告服务...
🔄 需要检查新公告
✅ 获取到最新公告: 欢迎使用岱宗文脉
📢 显示新公告
🔌 尝试连接WebSocket
✅ WebSocket连接成功
📨 收到WebSocket消息: {"type":"announcement_update"}
📢 收到公告更新通知
```

### 设置页面

提供专门的公告设置页面，用户可以：
- 查看WebSocket连接状态
- 手动刷新公告
- 强制刷新公告
- 清除缓存
- 查看功能说明

## 性能优化

### 1. 网络优化

- WebSocket连接复用
- 心跳机制减少无效连接
- 自动重连避免连接丢失

### 2. 缓存优化

- 智能缓存策略
- 按需清除缓存
- 避免重复请求

### 3. 电池优化

- 应用后台时降低检查频率
- WebSocket连接在应用关闭时自动断开
- 避免不必要的网络请求

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查网络连接
   - 确认服务器WebSocket端点可用
   - 查看重连日志

2. **公告不及时更新**
   - 检查应用生命周期状态
   - 确认检查间隔设置
   - 尝试手动刷新

3. **缓存问题**
   - 清除应用缓存
   - 重启应用
   - 检查SharedPreferences状态

### 调试命令

```dart
// 获取连接状态
final webSocketService = Get.find<WebSocketService>();
print('连接状态: ${webSocketService.isConnected.value}');

// 强制刷新公告
final announcementService = Get.find<AnnouncementService>();
await announcementService.forceRefreshAnnouncement();

// 清除缓存
await announcementService.clearCache();
```

## 未来改进

1. **推送通知集成**：集成Firebase Cloud Messaging
2. **离线支持**：改进离线状态下的公告缓存
3. **个性化设置**：允许用户自定义检查频率
4. **统计分析**：添加公告阅读统计功能
