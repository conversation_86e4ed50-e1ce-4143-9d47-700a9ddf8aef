import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../lib/services/announcement_service.dart';
import '../lib/services/websocket_service.dart';
import '../lib/screens/settings/announcement_settings_screen.dart';

/// 公告系统演示应用
class AnnouncementDemoApp extends StatelessWidget {
  const AnnouncementDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '公告系统演示',
      theme: ThemeData.dark(),
      home: const DemoHomePage(),
    );
  }
}

class DemoHomePage extends StatefulWidget {
  const DemoHomePage({super.key});

  @override
  State<DemoHomePage> createState() => _DemoHomePageState();
}

class _DemoHomePageState extends State<DemoHomePage> {
  late AnnouncementService announcementService;
  late WebSocketService webSocketService;

  @override
  void initState() {
    super.initState();
    
    // 初始化服务
    announcementService = Get.put(AnnouncementService());
    webSocketService = Get.put(WebSocketService());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('公告系统演示'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Get.to(() => const AnnouncementSettingsScreen()),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 实时状态卡片
            _buildStatusCard(),
            
            const SizedBox(height: 16),
            
            // 当前公告卡片
            _buildCurrentAnnouncementCard(),
            
            const SizedBox(height: 16),
            
            // 操作按钮
            _buildActionButtons(),
            
            const SizedBox(height: 16),
            
            // 演示说明
            _buildDemoInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      color: const Color(0xFF2A2A3E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '实时推送状态',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            Obx(() => Row(
              children: [
                Icon(
                  webSocketService.isConnected.value 
                      ? Icons.wifi 
                      : Icons.wifi_off,
                  color: webSocketService.isConnected.value 
                      ? Colors.green 
                      : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  webSocketService.connectionStatus.value,
                  style: const TextStyle(color: Colors.white70),
                ),
              ],
            )),
            const SizedBox(height: 8),
            Obx(() => Text(
              '检查频率：${announcementService.isLoading.value ? "检查中..." : "前台1分钟/后台5分钟"}',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentAnnouncementCard() {
    return Card(
      color: const Color(0xFF2A2A3E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '当前公告',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            Obx(() {
              final announcement = announcementService.announcement.value;
              if (announcement == null) {
                return const Text(
                  '暂无公告',
                  style: TextStyle(color: Colors.white70),
                );
              }
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    announcement.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    announcement.content,
                    style: const TextStyle(color: Colors.white70),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '发布时间：${announcement.date.toString().substring(0, 19)}',
                    style: const TextStyle(color: Colors.white54, fontSize: 12),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Card(
      color: const Color(0xFF2A2A3E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '操作演示',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => announcementService.refreshAnnouncement(),
                    icon: const Icon(Icons.refresh),
                    label: const Text('手动刷新'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => announcementService.forceRefreshAnnouncement(),
                    icon: const Icon(Icons.sync),
                    label: const Text('强制刷新'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => webSocketService.reconnect(),
                    icon: const Icon(Icons.wifi),
                    label: const Text('重连WebSocket'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => announcementService.clearCache(),
                    icon: const Icon(Icons.delete_sweep),
                    label: const Text('清除缓存'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoInfo() {
    return Card(
      color: const Color(0xFF2A2A3E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '演示说明',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '本演示展示了改进后的公告系统特性：\n\n'
              '1. 实时推送：WebSocket连接实现即时通知\n'
              '2. 智能缓存：前台1分钟、后台5分钟检查频率\n'
              '3. 生命周期感知：应用切换前台时自动检查\n'
              '4. 强制刷新：忽略时间限制立即获取最新公告\n'
              '5. 连接管理：自动重连和心跳保活机制\n\n'
              '测试方法：\n'
              '• 在后台管理页面更新公告\n'
              '• 观察应用是否立即收到推送\n'
              '• 切换应用前后台状态\n'
              '• 测试网络断开重连功能',
              style: TextStyle(
                color: Colors.white70,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  runApp(const AnnouncementDemoApp());
}
