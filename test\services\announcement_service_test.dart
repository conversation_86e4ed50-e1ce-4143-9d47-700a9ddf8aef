import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:novel_app/services/announcement_service.dart';

void main() {
  group('AnnouncementService Tests', () {
    late AnnouncementService announcementService;

    setUp(() async {
      // 初始化GetX
      Get.testMode = true;
      
      // 模拟SharedPreferences
      SharedPreferences.setMockInitialValues({});
      
      // 创建服务实例
      announcementService = AnnouncementService();
    });

    tearDown(() {
      Get.reset();
    });

    test('应该正确初始化公告服务', () {
      expect(announcementService.announcement.value, isNull);
      expect(announcementService.isLoading.value, isFalse);
    });

    test('应该正确初始化服务状态', () {
      // 验证服务初始化状态
      expect(announcementService.announcement.value, isNull);
      expect(announcementService.isLoading.value, isFalse);
    });

    test('强制刷新应该清除缓存', () async {
      // 设置一些模拟的SharedPreferences数据
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_announcement_id', 'test-id');
      await prefs.setInt('last_announcement_check_time', DateTime.now().millisecondsSinceEpoch);
      
      // 验证数据存在
      expect(prefs.getString('last_announcement_id'), equals('test-id'));
      expect(prefs.getInt('last_announcement_check_time'), isNotNull);
      
      // 调用强制刷新（这会清除缓存）
      try {
        await announcementService.forceRefreshAnnouncement();
      } catch (e) {
        // 忽略网络错误，我们只关心缓存清除
      }
      
      // 验证缓存已被清除
      expect(prefs.getString('last_announcement_id'), isNull);
      expect(prefs.getInt('last_announcement_check_time'), isNull);
    });

    test('清除缓存应该重置所有状态', () async {
      // 设置一些状态
      announcementService.announcement.value = Announcement(
        id: 'test-id',
        title: 'Test Title',
        content: 'Test Content',
        date: DateTime.now(),
      );
      
      // 清除缓存
      await announcementService.clearCache();
      
      // 验证状态已重置
      expect(announcementService.announcement.value, isNull);
    });
  });

  group('Announcement Model Tests', () {
    test('应该正确创建Announcement对象', () {
      final announcement = Announcement(
        id: 'test-id',
        title: 'Test Title',
        content: 'Test Content',
        date: DateTime.now(),
        isImportant: true,
        isActive: true,
      );

      expect(announcement.id, equals('test-id'));
      expect(announcement.title, equals('Test Title'));
      expect(announcement.content, equals('Test Content'));
      expect(announcement.isImportant, isTrue);
      expect(announcement.isActive, isTrue);
    });

    test('应该正确从JSON创建Announcement对象', () {
      final json = {
        'id': 'test-id',
        'title': 'Test Title',
        'content': 'Test Content',
        'created_at': '2025-01-27T10:00:00Z',
        'is_important': true,
        'is_active': true,
      };

      final announcement = Announcement.fromJson(json);

      expect(announcement.id, equals('test-id'));
      expect(announcement.title, equals('Test Title'));
      expect(announcement.content, equals('Test Content'));
      expect(announcement.isImportant, isTrue);
      expect(announcement.isActive, isTrue);
    });

    test('应该正确转换为JSON', () {
      final announcement = Announcement(
        id: 'test-id',
        title: 'Test Title',
        content: 'Test Content',
        date: DateTime.parse('2025-01-27T10:00:00Z'),
        isImportant: true,
        isActive: true,
      );

      final json = announcement.toJson();

      expect(json['id'], equals('test-id'));
      expect(json['title'], equals('Test Title'));
      expect(json['content'], equals('Test Content'));
      expect(json['created_at'], equals('2025-01-27T10:00:00.000Z'));
      expect(json['is_important'], isTrue);
      expect(json['is_active'], isTrue);
    });
  });
}
