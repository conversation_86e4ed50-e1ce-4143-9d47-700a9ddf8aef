import 'dart:convert';
import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import '../screens/announcement_screen.dart';

/// 公告服务 - 从服务器动态获取公告
class AnnouncementService extends GetxService {
  static const String _lastAnnouncementIdKey = 'last_announcement_id';
  static const String _cachedAnnouncementKey = 'cached_announcement';
  static const String _lastCheckTimeKey = 'last_announcement_check_time';

  final announcement = Rxn<Announcement>();
  final RxBool isLoading = false.obs;

  // 静态公告文件地址
  final String _announcementUrl = '${ApiConfig.baseUrl}/announcement.json';
  final String _backupAnnouncementUrl = '${ApiConfig.backupUrl}/announcement.json';

  // 检查间隔（秒）- 实时推送，每30秒检查一次
  static const int _checkIntervalSeconds = 30;

  @override
  void onInit() {
    super.onInit();
    initAnnouncement();
    // 启动实时检查定时器
    _startRealTimeCheck();
  }

  /// 初始化公告服务
  Future<void> initAnnouncement() async {
    try {
      print('🔔 初始化公告服务...');
      
      // 检查是否需要获取新公告
      if (await _shouldCheckForNewAnnouncement()) {
        print('🔄 需要检查新公告');
        // 异步获取最新公告，不阻塞应用启动
        _fetchLatestAnnouncementAsync();
      } else {
        print('📋 加载缓存公告');
        // 加载缓存的公告（如果有未读的）
        await _loadCachedAnnouncement();
      }
    } catch (e) {
      print('❌ 初始化公告服务失败: $e');
    }
  }

  /// 检查是否应该获取新公告
  Future<bool> _shouldCheckForNewAnnouncement() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCheckTime = prefs.getInt(_lastCheckTimeKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDiff = now - lastCheckTime;
      final hoursDiff = timeDiff / (1000 * 60 * 60);
      
      // 实时推送模式，总是检查最新公告
      return true;
    } catch (e) {
      print('检查时间间隔失败: $e');
      return true; // 出错时默认检查
    }
  }

  /// 加载缓存的公告
  Future<void> _loadCachedAnnouncement() async {
    try {
      final cachedAnnouncement = await _getCachedAnnouncement();
      if (cachedAnnouncement != null) {
        final prefs = await SharedPreferences.getInstance();
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);
        
        // 如果有缓存公告且未读，显示它
        if (lastAnnouncementId != cachedAnnouncement.id) {
          announcement.value = cachedAnnouncement;
          print('📢 显示缓存公告: ${cachedAnnouncement.title}');
          // 自动弹出缓存的公告
          _showAnnouncementDialog(cachedAnnouncement);
        }
      }
    } catch (e) {
      print('加载缓存公告失败: $e');
    }
  }

  /// 异步获取最新公告
  Future<void> _fetchLatestAnnouncementAsync() async {
    try {
      isLoading.value = true;
      
      // 获取最新公告
      final latestAnnouncement = await _fetchLatestAnnouncement();
      
      if (latestAnnouncement != null) {
        print('✅ 获取到最新公告: ${latestAnnouncement.title}');
        
        // 保存到缓存
        await _saveCachedAnnouncement(latestAnnouncement);
        
        // 更新检查时间
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_lastCheckTimeKey, DateTime.now().millisecondsSinceEpoch);
        
        // 检查是否是新公告
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);
        if (lastAnnouncementId != latestAnnouncement.id) {
          announcement.value = latestAnnouncement;
          print('🆕 显示新公告');
          // 自动弹出公告对话框
          _showAnnouncementDialog(latestAnnouncement);
        } else {
          print('📋 公告已读过');
        }
      } else {
        print('ℹ️ 没有获取到公告');
      }
    } catch (e) {
      print('❌ 获取公告失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 从静态文件获取最新公告
  Future<Announcement?> _fetchLatestAnnouncement() async {
    print('🌐 开始获取公告...');
    print('📡 主域名URL: $_announcementUrl');
    print('📡 备用域名URL: $_backupAnnouncementUrl');

    try {
      final headers = {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
        'User-Agent': 'DaizongNovelApp/1.0',
      };

      final client = http.Client();
      try {
        final timeout = Duration(seconds: ApiConfig.connectionTimeout);

        // 先尝试主域名
        try {
          print('🔄 尝试主域名: $_announcementUrl');
          final response = await client
              .get(Uri.parse(_announcementUrl), headers: headers)
              .timeout(timeout);

          print('📊 主域名响应状态: ${response.statusCode}');
          print('📄 主域名响应长度: ${response.body.length}');

          if (response.statusCode == 200 && response.body.isNotEmpty) {
            print('✅ 主域名响应成功，解析JSON...');
            print('📝 响应内容: ${response.body}');

            final data = json.decode(response.body);
            print('🔍 解析后的数据: $data');
            print('🔍 is_active 值: ${data['is_active']}');

            // 检查公告是否激活
            if (data['is_active'] == true) {
              print('✅ 公告已激活，创建Announcement对象');
              final announcement = Announcement.fromJson(data);
              print('🎯 成功创建公告: ${announcement.title}');
              return announcement;
            } else {
              print('⚠️ 公告未激活 (is_active = ${data['is_active']})');
            }
          } else {
            print('❌ 主域名响应失败: 状态码=${response.statusCode}, 内容长度=${response.body.length}');
          }
        } catch (e) {
          print('❌ 主域名获取公告失败，尝试备用地址: $e');

          // 如果主域名失败，尝试备用地址
          try {
            print('🔄 尝试备用域名: $_backupAnnouncementUrl');
            final response = await client
                .get(Uri.parse(_backupAnnouncementUrl), headers: headers)
                .timeout(timeout);

            print('📊 备用域名响应状态: ${response.statusCode}');
            print('📄 备用域名响应长度: ${response.body.length}');

            if (response.statusCode == 200 && response.body.isNotEmpty) {
              print('✅ 备用域名响应成功，解析JSON...');
              print('📝 响应内容: ${response.body}');

              final data = json.decode(response.body);
              print('🔍 解析后的数据: $data');
              print('🔍 is_active 值: ${data['is_active']}');

              // 检查公告是否激活
              if (data['is_active'] == true) {
                print('✅ 公告已激活，创建Announcement对象');
                final announcement = Announcement.fromJson(data);
                print('🎯 成功创建公告: ${announcement.title}');
                return announcement;
              } else {
                print('⚠️ 公告未激活 (is_active = ${data['is_active']})');
              }
            } else {
              print('❌ 备用域名响应失败: 状态码=${response.statusCode}, 内容长度=${response.body.length}');
            }
          } catch (backupError) {
            print('❌ 备用域名也失败: $backupError');
          }
        }

        print('❌ 所有域名都无法获取有效公告');
        return null;
      } finally {
        client.close();
      }
    } catch (e) {
      print('❌ 获取公告网络请求失败: $e');
      return null;
    }
  }

  /// 获取缓存的公告
  Future<Announcement?> _getCachedAnnouncement() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cachedAnnouncementKey);
      if (cachedData != null) {
        final data = json.decode(cachedData);
        return Announcement.fromJson(data);
      }
    } catch (e) {
      print('读取缓存公告失败: $e');
    }
    return null;
  }

  /// 保存公告到缓存
  Future<void> _saveCachedAnnouncement(Announcement announcement) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(announcement.toJson());
      await prefs.setString(_cachedAnnouncementKey, data);
    } catch (e) {
      print('保存缓存公告失败: $e');
    }
  }

  /// 标记公告为已读
  Future<void> markAnnouncementAsRead() async {
    try {
      if (announcement.value != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_lastAnnouncementIdKey, announcement.value!.id);
        announcement.value = null;
        print('✅ 公告已标记为已读');
      }
    } catch (e) {
      print('标记公告已读失败: $e');
    }
  }

  /// 手动刷新公告
  Future<void> refreshAnnouncement() async {
    try {
      print('🔄 手动刷新公告...');

      // 先清除已读状态，这样即使是相同的公告也会重新显示
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastAnnouncementIdKey);
      print('🗑️ 已清除公告已读状态');

      await _fetchLatestAnnouncementAsync();

      if (announcement.value == null) {
        Get.snackbar(
          '提示',
          '没有新的公告',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.surface,
          colorText: Get.theme.colorScheme.onSurface,
        );
      }
    } catch (e) {
      print('刷新公告失败: $e');
      Get.snackbar(
        '错误',
        '刷新公告失败',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error.withOpacity(0.1),
        colorText: Get.theme.colorScheme.error,
      );
    }
  }

  /// 显示公告对话框
  void _showAnnouncementDialog(Announcement announcement) {
    try {
      // 确保在UI线程中执行
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (Get.context != null) {
          Get.dialog(
            AnnouncementScreen(announcement: announcement),
            barrierDismissible: false,
          );
          print('📢 公告对话框已显示');
        } else {
          print('⚠️ 无法显示公告对话框：Get.context 为空');
        }
      });
    } catch (e) {
      print('显示公告对话框失败: $e');
    }
  }

  /// 清除所有公告缓存（用于调试）
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastAnnouncementIdKey);
      await prefs.remove(_cachedAnnouncementKey);
      await prefs.remove(_lastCheckTimeKey);
      announcement.value = null;
      print('🗑️ 公告缓存已清除');
    } catch (e) {
      print('清除缓存失败: $e');
    }
  }

  /// 启动实时检查定时器
  void _startRealTimeCheck() {
    print('🔄 启动实时公告检查定时器，每${_checkIntervalSeconds}秒检查一次');

    // 使用Timer.periodic创建定时器
    Timer.periodic(Duration(seconds: _checkIntervalSeconds), (timer) async {
      try {
        print('⏰ 定时检查公告更新...');
        await _checkForNewAnnouncementRealTime();
      } catch (e) {
        print('❌ 定时检查公告失败: $e');
      }
    });
  }

  /// 实时检查新公告
  Future<void> _checkForNewAnnouncementRealTime() async {
    try {
      // 获取最新公告
      final latestAnnouncement = await _fetchLatestAnnouncement();

      if (latestAnnouncement != null) {
        // 获取上次显示的公告ID
        final prefs = await SharedPreferences.getInstance();
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);

        // 如果是新公告，立即显示
        if (lastAnnouncementId != latestAnnouncement.id) {
          print('🆕 发现新公告: ${latestAnnouncement.title}');

          // 保存到缓存
          await _saveCachedAnnouncement(latestAnnouncement);

          // 更新公告状态
          announcement.value = latestAnnouncement;

          // 立即显示公告对话框
          _showAnnouncementDialog(latestAnnouncement);
        } else {
          print('📋 公告无更新');
        }
      } else {
        print('ℹ️ 服务器无公告');
      }
    } catch (e) {
      print('❌ 实时检查公告失败: $e');
    }
  }
}

/// 公告数据模型
class Announcement {
  final String id;
  final String title;
  final String content;
  final DateTime date;
  final bool isImportant;
  final bool isActive;

  Announcement({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    this.isImportant = false,
    this.isActive = true,
  });

  /// 从JSON创建Announcement对象
  factory Announcement.fromJson(Map<String, dynamic> json) {
    return Announcement(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      date: DateTime.parse(json['created_at'] as String),
      isImportant: json['is_important'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'created_at': date.toIso8601String(),
      'is_important': isImportant,
      'is_active': isActive,
    };
  }
}
