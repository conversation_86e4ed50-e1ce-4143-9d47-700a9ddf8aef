import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/announcement_service.dart';
import '../../services/websocket_service.dart';

/// 公告设置页面
class AnnouncementSettingsScreen extends StatelessWidget {
  const AnnouncementSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final announcementService = Get.find<AnnouncementService>();
    final webSocketService = Get.find<WebSocketService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('公告设置'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF1A1A2E),
              const Color(0xFF16213E),
              const Color(0xFF0F3460),
            ],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 实时推送状态卡片
            _buildStatusCard(webSocketService),
            
            const SizedBox(height: 16),
            
            // 公告管理卡片
            _buildAnnouncementCard(announcementService),
            
            const SizedBox(height: 16),
            
            // 缓存管理卡片
            _buildCacheCard(announcementService),
            
            const SizedBox(height: 16),
            
            // 说明信息
            _buildInfoCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(WebSocketService webSocketService) {
    return Card(
      color: const Color(0xFF2A2A3E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.wifi, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  '实时推送状态',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Obx(() => Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: webSocketService.isConnected.value 
                        ? Colors.green 
                        : Colors.red,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  webSocketService.connectionStatus.value,
                  style: const TextStyle(color: Colors.white70),
                ),
              ],
            )),
            const SizedBox(height: 12),
            if (!webSocketService.isConnected.value)
              ElevatedButton.icon(
                onPressed: () => webSocketService.reconnect(),
                icon: const Icon(Icons.refresh),
                label: const Text('重新连接'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnnouncementCard(AnnouncementService announcementService) {
    return Card(
      color: const Color(0xFF2A2A3E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.announcement, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  '公告管理',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Obx(() => Text(
              announcementService.announcement.value != null
                  ? '当前公告：${announcementService.announcement.value!.title}'
                  : '暂无公告',
              style: const TextStyle(color: Colors.white70),
            )),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => announcementService.refreshAnnouncement(),
                    icon: const Icon(Icons.refresh),
                    label: const Text('手动刷新'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => announcementService.forceRefreshAnnouncement(),
                    icon: const Icon(Icons.sync),
                    label: const Text('强制刷新'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCacheCard(AnnouncementService announcementService) {
    return Card(
      color: const Color(0xFF2A2A3E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.storage, color: Colors.cyan),
                const SizedBox(width: 8),
                const Text(
                  '缓存管理',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '清除缓存将删除所有已保存的公告数据，下次启动时会重新获取。',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: () => _showClearCacheDialog(announcementService),
              icon: const Icon(Icons.delete_sweep),
              label: const Text('清除缓存'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: const Color(0xFF2A2A3E),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  '功能说明',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• 实时推送：通过WebSocket连接实现公告的实时推送\n'
              '• 智能缓存：应用前台时每1分钟检查，后台时每5分钟检查\n'
              '• 自动显示：新公告会自动弹出显示\n'
              '• 手动刷新：可随时手动检查最新公告\n'
              '• 强制刷新：忽略缓存时间限制，强制获取最新公告',
              style: TextStyle(
                color: Colors.white70,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearCacheDialog(AnnouncementService announcementService) {
    Get.dialog(
      AlertDialog(
        backgroundColor: const Color(0xFF2A2A3E),
        title: const Text(
          '确认清除缓存',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          '这将清除所有公告缓存数据，确定要继续吗？',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              announcementService.clearCache();
              Get.back();
              Get.snackbar(
                '成功',
                '公告缓存已清除',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green.withOpacity(0.1),
                colorText: Colors.green,
              );
            },
            child: const Text(
              '确定',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
