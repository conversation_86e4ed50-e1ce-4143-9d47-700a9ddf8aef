import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import '../config/api_config.dart';
import 'announcement_service.dart';

/// WebSocket服务 - 用于实时推送公告更新
class WebSocketService extends GetxService {
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;
  
  // 连接状态
  final RxBool isConnected = false.obs;
  final RxString connectionStatus = '未连接'.obs;
  
  // 重连配置
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 5);
  static const Duration _heartbeatInterval = Duration(seconds: 30);
  
  int _reconnectAttempts = 0;
  bool _shouldReconnect = true;

  @override
  void onInit() {
    super.onInit();
    // 只在非Web平台启用WebSocket
    if (!kIsWeb) {
      _initWebSocket();
    }
  }

  @override
  void onClose() {
    _shouldReconnect = false;
    _disconnect();
    super.onClose();
  }

  /// 初始化WebSocket连接
  void _initWebSocket() {
    try {
      final wsUrl = _getWebSocketUrl();
      print('🔌 尝试连接WebSocket: $wsUrl');
      
      connectionStatus.value = '连接中...';
      
      _channel = IOWebSocketChannel.connect(
        Uri.parse(wsUrl),
        headers: {
          'User-Agent': 'DaiZhongNovelApp/1.0',
        },
      );

      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      isConnected.value = true;
      connectionStatus.value = '已连接';
      _reconnectAttempts = 0;
      
      // 启动心跳
      _startHeartbeat();
      
      print('✅ WebSocket连接成功');
    } catch (e) {
      print('❌ WebSocket连接失败: $e');
      _onError(e);
    }
  }

  /// 获取WebSocket URL
  String _getWebSocketUrl() {
    // 将HTTP URL转换为WebSocket URL
    final baseUrl = ApiConfig.baseUrl;
    final wsUrl = baseUrl
        .replaceFirst('http://', 'ws://')
        .replaceFirst('https://', 'wss://');
    return '$wsUrl/ws/announcements';
  }

  /// 处理接收到的消息
  void _onMessage(dynamic message) {
    try {
      print('📨 收到WebSocket消息: $message');
      
      final data = json.decode(message.toString());
      final type = data['type'] as String?;
      
      switch (type) {
        case 'announcement_update':
          _handleAnnouncementUpdate(data);
          break;
        case 'pong':
          print('💓 收到心跳响应');
          break;
        default:
          print('🤷 未知消息类型: $type');
      }
    } catch (e) {
      print('处理WebSocket消息失败: $e');
    }
  }

  /// 处理公告更新
  void _handleAnnouncementUpdate(Map<String, dynamic> data) {
    try {
      print('📢 收到公告更新通知');
      
      // 获取公告服务实例
      final announcementService = Get.find<AnnouncementService>();
      
      // 强制刷新公告
      announcementService.forceRefreshAnnouncement();
      
      // 显示通知
      Get.snackbar(
        '📢 新公告',
        '收到新的公告更新',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Get.theme.colorScheme.primary.withOpacity(0.1),
        colorText: Get.theme.colorScheme.primary,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      print('处理公告更新失败: $e');
    }
  }

  /// 处理连接错误
  void _onError(dynamic error) {
    print('❌ WebSocket错误: $error');
    isConnected.value = false;
    connectionStatus.value = '连接错误';
    
    _stopHeartbeat();
    _scheduleReconnect();
  }

  /// 处理连接断开
  void _onDisconnected() {
    print('🔌 WebSocket连接已断开');
    isConnected.value = false;
    connectionStatus.value = '连接断开';
    
    _stopHeartbeat();
    _scheduleReconnect();
  }

  /// 安排重连
  void _scheduleReconnect() {
    if (!_shouldReconnect || _reconnectAttempts >= _maxReconnectAttempts) {
      print('🚫 停止重连尝试');
      connectionStatus.value = '连接失败';
      return;
    }

    _reconnectAttempts++;
    connectionStatus.value = '重连中... (${_reconnectAttempts}/$_maxReconnectAttempts)';
    
    print('🔄 安排重连，尝试次数: $_reconnectAttempts');
    
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectDelay, () {
      if (_shouldReconnect) {
        _disconnect();
        _initWebSocket();
      }
    });
  }

  /// 启动心跳
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (isConnected.value) {
        _sendHeartbeat();
      }
    });
  }

  /// 停止心跳
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// 发送心跳
  void _sendHeartbeat() {
    try {
      final heartbeat = json.encode({'type': 'ping'});
      _channel?.sink.add(heartbeat);
      print('💓 发送心跳');
    } catch (e) {
      print('发送心跳失败: $e');
    }
  }

  /// 断开连接
  void _disconnect() {
    _subscription?.cancel();
    _subscription = null;
    
    _channel?.sink.close();
    _channel = null;
    
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
    
    _stopHeartbeat();
    
    isConnected.value = false;
  }

  /// 手动重连
  void reconnect() {
    print('🔄 手动重连WebSocket');
    _reconnectAttempts = 0;
    _disconnect();
    _initWebSocket();
  }
}
