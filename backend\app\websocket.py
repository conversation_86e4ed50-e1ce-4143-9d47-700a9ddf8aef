"""
WebSocket支持模块 - 用于实时推送公告更新
"""
import json
import asyncio
import logging
from typing import Set, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

logger = logging.getLogger(__name__)

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接
        self.active_connections: Set[WebSocket] = set()
        # 连接统计
        self.connection_count = 0
        
    async def connect(self, websocket: WebSocket):
        """接受新的WebSocket连接"""
        await websocket.accept()
        self.active_connections.add(websocket)
        self.connection_count += 1
        logger.info(f"新的WebSocket连接，当前连接数: {len(self.active_connections)}")
        
        # 发送欢迎消息
        await self.send_personal_message({
            "type": "welcome",
            "message": "WebSocket连接成功",
            "timestamp": datetime.now().isoformat()
        }, websocket)
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any]):
        """广播消息给所有连接的客户端"""
        if not self.active_connections:
            logger.info("没有活跃的WebSocket连接，跳过广播")
            return
            
        logger.info(f"广播消息给 {len(self.active_connections)} 个客户端")
        
        # 需要移除的无效连接
        disconnected = set()
        
        for connection in self.active_connections.copy():
            try:
                await connection.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.add(connection)
        
        # 清理无效连接
        for connection in disconnected:
            self.disconnect(connection)
    
    async def broadcast_announcement_update(self, announcement_data: Dict[str, Any] = None):
        """广播公告更新通知"""
        message = {
            "type": "announcement_update",
            "timestamp": datetime.now().isoformat(),
            "data": announcement_data or {}
        }
        await self.broadcast(message)
        logger.info("已广播公告更新通知")

# 全局连接管理器实例
manager = ConnectionManager()

async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点处理函数"""
    await manager.connect(websocket)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_client_message(message, websocket)
            except json.JSONDecodeError:
                await manager.send_personal_message({
                    "type": "error",
                    "message": "无效的JSON格式"
                }, websocket)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("客户端主动断开WebSocket连接")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {e}")
        manager.disconnect(websocket)

async def handle_client_message(message: Dict[str, Any], websocket: WebSocket):
    """处理客户端消息"""
    message_type = message.get("type")
    
    if message_type == "ping":
        # 心跳响应
        await manager.send_personal_message({
            "type": "pong",
            "timestamp": datetime.now().isoformat()
        }, websocket)
        
    elif message_type == "get_status":
        # 获取连接状态
        await manager.send_personal_message({
            "type": "status",
            "connected_clients": len(manager.active_connections),
            "timestamp": datetime.now().isoformat()
        }, websocket)
        
    else:
        logger.warning(f"未知的消息类型: {message_type}")
        await manager.send_personal_message({
            "type": "error",
            "message": f"未知的消息类型: {message_type}"
        }, websocket)

# 公告更新通知函数（供其他模块调用）
async def notify_announcement_update(announcement_data: Dict[str, Any] = None):
    """通知公告更新（供外部调用）"""
    await manager.broadcast_announcement_update(announcement_data)

def get_connection_count() -> int:
    """获取当前连接数"""
    return len(manager.active_connections)
